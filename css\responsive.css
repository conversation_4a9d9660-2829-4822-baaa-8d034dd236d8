/* Responsive Design - Mobile First Approach */

/* Large Tablets and Small Desktops */
@media (max-width: 1024px) {
    .hero-container {
        gap: var(--spacing-xl);
    }
    
    .hero-title {
        font-size: var(--font-size-4xl);
    }
    
    .about-content {
        gap: var(--spacing-xl);
    }
    
    .contact-content {
        gap: var(--spacing-xl);
    }
}

/* Tablets */
@media (max-width: 768px) {
    /* Navigation */
    .nav-container {
        position: relative;
    }
    
    .nav-toggle {
        display: flex;
        margin-right: 10px;
    }
    

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--bg-color);
        width: 90%;
        text-align: center;
        transition: 0.3s;
        box-shadow: var(--shadow);
        padding: var(--spacing-lg) 0;
        border-top: 1px solid var(--border-color);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu li {
        margin: var(--spacing-sm) 0;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active .bar:nth-child(1) {
        transform: translateY(7px) rotate(45deg);
    }

    .nav-toggle.active .bar:nth-child(3) {
        transform: translateY(-7px) rotate(-45deg);
    }

    /* Hero Section */
    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--spacing-lg);
    }

    .hero-title {
        font-size: var(--font-size-3xl);
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .profile-img {
        width: 250px;
        height: 250px;
    }

    /* About Section */
    .about-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .about-stats {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    /* Skills Section */
    .skills-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    /* Projects Section */
    .projects-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    /* Contact Section */
    .contact-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .contact-form {
        order: -1;
    }

    /* Footer */
    .footer-content {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }
}

/* Mobile Phones */
@media (max-width: 480px) {
    /* Container */
    .container {
        padding: 0 var(--spacing-sm);
    }

    /* Navigation */
    .nav-container {
        padding: 0 var(--spacing-sm);
    }

    .nav-logo a {
        font-size: var(--font-size-lg);
    }

    /* Hero Section */
    .hero {
        padding: var(--spacing-4xl) 0 var(--spacing-xl);
        min-height: 130vh;
    }
    
    .hero-container {
        padding-top: 30px; /* Add extra padding to prevent overlap with navbar */
    }

    .hero-title {
        font-size: var(--font-size-2xl);
    }

    .hero-subtitle {
        font-size: var(--font-size-lg);
    }

    .hero-description {
        font-size: var(--font-size-base);
    }

    .hero-social {
        justify-content: center;
    }

    .hero-social a {
        width: 45px;
        height: 45px;
    }

    .profile-img {
        width: 200px;
        height: 200px;
    }

    /* Sections */
    section {
        padding: var(--spacing-xl) 0;
    }

    .section-title {
        font-size: var(--font-size-2xl);
        margin-bottom: var(--spacing-xl);
    }

    /* About Section */
    .about-text p {
        font-size: var(--font-size-base);
    }

    .stat {
        padding: var(--spacing-sm);
    }

    .stat h3 {
        font-size: var(--font-size-xl);
    }

    /* Skills Section */
    .skill-category {
        padding: var(--spacing-lg);
    }

    .skill-category h3 {
        font-size: var(--font-size-lg);
    }

    .skill-items {
        gap: var(--spacing-xs);
    }

    .skill-item {
        font-size: var(--font-size-xs);
        padding: 6px var(--spacing-sm);
    }

    /* Projects Section */
    .project-content {
        padding: var(--spacing-md);
    }

    .project-content h3 {
        font-size: var(--font-size-lg);
    }

    .project-image {
        height: 180px;
    }

    /* Contact Section */
    .contact-info h3 {
        font-size: var(--font-size-xl);
    }

    .contact-info p {
        font-size: var(--font-size-base);
    }

    .contact-form {
        padding: var(--spacing-lg);
    }

    .form-group input,
    .form-group textarea {
        padding: var(--spacing-sm);
    }

    /* Buttons */
    .btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
    }

    /* Footer */
    .footer-social a {
        width: 35px;
        height: 35px;
        font-size: var(--font-size-sm);
    }
}

/* Extra Small Devices */
@media (max-width: 320px) {
    .hero-title {
        font-size: var(--font-size-xl);
    }

    .section-title {
        font-size: var(--font-size-xl);
    }

    .profile-img {
        width: 180px;
        height: 180px;
    }

    .skill-category {
        padding: var(--spacing-md);
    }

    .contact-form {
        padding: var(--spacing-md);
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .profile-img,
    .about-img-container img,
    .project-image img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Landscape Orientation on Mobile */
@media (max-height: 500px) and (orientation: landscape) {
    .hero {
        min-height: 100vh;
        padding: var(--spacing-lg) 0;
    }

    .hero-container {
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-md);
    }

    .hero-title {
        font-size: var(--font-size-2xl);
    }

    .profile-img {
        width: 150px;
        height: 150px;
    }
}

/* Print Styles */
@media print {
    * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }

    .navbar,
    .hero-social,
    .contact-form,
    .scroll-indicator {
        display: none !important;
    }

    .hero {
        min-height: auto;
        padding: 1rem 0;
    }

    section {
        padding: 1rem 0;
        page-break-inside: avoid;
    }

    .section-title {
        page-break-after: avoid;
    }

    .project-card,
    .skill-category {
        page-break-inside: avoid;
        border: 1px solid #ccc;
        margin-bottom: 1rem;
    }

    a {
        text-decoration: underline;
    }

    a[href^="http"]:after {
        content: " (" attr(href) ")";
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .scroll-indicator {
        animation: none;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.5);
    }

    .btn-primary {
        border: 2px solid #000000;
    }

    .btn-secondary {
        border: 2px solid #000000;
        background-color: #ffffff;
    }
}
