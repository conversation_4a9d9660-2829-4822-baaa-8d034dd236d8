/* CSS Variables for Neumorphism Theme */
:root {
    /* Primary Colors */
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary-color: #6366f1;
    --accent-color: #f59e0b;

    /* Light Neumorphism Theme */
    --bg-color: #e0e5ec;
    --bg-secondary: #e8edf4;
    --bg-tertiary: #f0f5fa;
    --text-primary: #2d3748;
    --text-secondary: #4a5568;
    --text-light: #718096;

    /* Neumorphism Shadows */
    --shadow-light: #ffffff;
    --shadow-dark: #c2c8d0;
    --shadow-inset-light: #f0f5fa;
    --shadow-inset-dark: #c8ccd1;

    /* Neumorphism Effects */
    --neu-shadow: 8px 8px 16px var(--shadow-dark), -8px -8px 16px var(--shadow-light);
    --neu-shadow-sm: 4px 4px 8px var(--shadow-dark), -4px -4px 8px var(--shadow-light);
    --neu-shadow-lg: 12px 12px 24px var(--shadow-dark), -12px -12px 24px var(--shadow-light);
    --neu-inset: inset 4px 4px 8px var(--shadow-inset-dark), inset -4px -4px 8px var(--shadow-inset-light);
    --neu-inset-sm: inset 2px 2px 4px var(--shadow-inset-dark), inset -2px -2px 4px var(--shadow-inset-light);

    /* Typography */
    --font-family: 'Inter', 'Segoe UI', sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;

    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    --spacing-2xl: 4rem;

    /* Neumorphism Border Radius */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;
    --radius-2xl: 24px;

    /* Transitions */
    --transition: all 0.3s ease;
    --transition-fast: all 0.15s ease;
}

/* Dark Neumorphism Theme */
[data-theme="dark"] {
    /* Dark Neumorphism Colors */
    --bg-color: #2a2d3a;
    --bg-secondary: #323644;
    --bg-tertiary: #3a3f4e;
    --text-primary: #e2e8f0;
    --text-secondary: #cbd5e0;
    --text-light: #a0aec0;

    /* Dark Neumorphism Shadows */
    --shadow-light: #363b4a;
    --shadow-dark: #1e1f26;
    --shadow-inset-light: #363b4a;
    --shadow-inset-dark: #1e1f26;

    /* Dark Neumorphism Effects */
    --neu-shadow: 8px 8px 16px var(--shadow-dark), -8px -8px 16px var(--shadow-light);
    --neu-shadow-sm: 4px 4px 8px var(--shadow-dark), -4px -4px 8px var(--shadow-light);
    --neu-shadow-lg: 12px 12px 24px var(--shadow-dark), -12px -12px 24px var(--shadow-light);
    --neu-inset: inset 4px 4px 8px var(--shadow-inset-dark), inset -4px -4px 8px var(--shadow-inset-light);
    --neu-inset-sm: inset 2px 2px 4px var(--shadow-inset-dark), inset -2px -2px 4px var(--shadow-inset-light);
}

/* Dark theme specific overrides */
[data-theme="dark"] .project-overlay {
    background: rgba(42, 45, 58, 0.95);
}

[data-theme="dark"] .contact {
    background: var(--bg-color);
}

/* Keep contact form in light mode styling even in dark theme - Subtle white glow */
[data-theme="dark"] .contact-form {
    background: #e0e5ec !important;
    box-shadow: 10px 10px 30px #c2c8d0, -10px -10px 30px #f8f9fa !important;
}

[data-theme="dark"] .contact-form .form-group input,
[data-theme="dark"] .contact-form .form-group textarea {
    background: #e0e5ec !important;
    box-shadow: inset 4px 4px 8px #c8ccd1, inset -4px -4px 8px #eef3f8 !important;
    color: #2d3748 !important;
}

[data-theme="dark"] .contact-form .form-group input::placeholder,
[data-theme="dark"] .contact-form .form-group textarea::placeholder {
    color: #8a8a8a !important;
}

[data-theme="dark"] .contact-form .form-group input:focus,
[data-theme="dark"] .contact-form .form-group textarea:focus {
    box-shadow: inset 6px 6px 12px #c2c8d0, inset -6px -6px 12px #f0f4f8 !important;
    background: #dde3ea !important;
}

[data-theme="dark"] .contact-form .btn {
    background: #e0e5ec !important;
    color: #2d3748 !important;
    box-shadow: 8px 8px 16px #c2c8d0, -8px -8px 16px #f5f7fa !important;
}

[data-theme="dark"] .contact-form .btn:hover {
    background: #d6dce4 !important;
    box-shadow: 6px 6px 12px #c2c8d0, -6px -6px 12px #f2f5f8 !important;
}

[data-theme="dark"] .contact-form .btn:active {
    box-shadow: inset 4px 4px 8px #c2c8d0, inset -4px -4px 8px #f0f4f8 !important;
}

/* Error states for contact form in dark mode - keep light styling */
[data-theme="dark"] .contact-form .form-group input.error,
[data-theme="dark"] .contact-form .form-group textarea.error {
    background: #f0d5d5 !important;
    box-shadow: inset 4px 4px 8px #d4b8b8, inset -4px -4px 8px #ffffff !important;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-color);
    transition: var(--transition);
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-sm);
}

/* Neumorphism Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: var(--bg-color);
    backdrop-filter: blur(10px);
    box-shadow: var(--neu-shadow-sm);
    z-index: 1000;
    transition: var(--transition);
    padding: 8px 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-sm);
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo a {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

.nav-logo a:hover {
    color: var(--primary-dark);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: var(--spacing-lg);
}

.nav-link {
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    position: relative;
    padding: 8px 16px;
    border-radius: var(--radius-md);
    background: var(--bg-color);
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
    box-shadow: var(--neu-inset-sm);
    background: var(--bg-secondary);
}

.nav-link::after {
    display: none;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.bar {
    width: 25px;
    height: 3px;
    background-color: var(--text-primary);
    transition: var(--transition);
}

.theme-toggle {
    background: var(--bg-color);
    border: none;
    border-radius: var(--radius-md);
    padding: 12px;
    cursor: pointer;
    color: var(--text-primary);
    transition: var(--transition);
    margin-left: var(--spacing-sm);
    box-shadow: var(--neu-shadow-sm);
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-toggle:hover {
    box-shadow: var(--neu-inset-sm);
    color: var(--primary-color);
    transform: translateY(-1px);
}

.theme-toggle:active {
    box-shadow: var(--neu-inset);
    transform: translateY(0);
}

/* Neumorphism Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: var(--spacing-2xl) 0;
    background: var(--bg-color);
    position: relative;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-sm);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    align-items: center;
}

.hero-title {
    font-size: var(--font-size-5xl);
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--spacing-sm);
}

.highlight {
    color: var(--primary-color);
    position: relative;
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
}

.hero-description {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    line-height: 1.7;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

/* Neumorphism Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-lg);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition);
    border: none;
    background: var(--bg-color);
    box-shadow: var(--neu-shadow);
    color: var(--text-primary);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    box-shadow: var(--neu-shadow);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: var(--neu-shadow-lg);
}

.btn-primary:active {
    transform: translateY(-1px);
    box-shadow: var(--neu-shadow-sm);
}

/* Custom Resume Download Button */
.btn-secondary[download] {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 200px;
    height: 100px;
    border-radius: var(--radius-lg);
    background: var(--bg-color);
    box-shadow: var(--neu-shadow-sm);
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
}

.btn-secondary[download]:hover {
    box-shadow: var(--neu-inset-sm);
    color: var(--primary-color);
    transform: translateY(-2px);
}

.btn-secondary[download]:active {
    box-shadow: var(--neu-inset);
    transform: translateY(0);
}

@keyframes downloadBounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(-2px);
    }
    40% {
        transform: translateY(-6px);
    }
    60% {
        transform: translateY(-4px);
    }
}

.hero-social {
    display: flex;
    gap: var(--spacing-md);
}

.hero-social a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    border-radius: var(--radius-lg);
    background: var(--bg-color);
    box-shadow: var(--neu-shadow-sm);
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
}

.hero-social a:hover {
    box-shadow: var(--neu-inset-sm);
    color: var(--primary-color);
    transform: translateY(-2px);
}

.hero-social a:active {
    box-shadow: var(--neu-inset);
    transform: translateY(0);
}

.hero-image {
    display: flex;
    justify-content: center;
}

.profile-card {
    position: relative;
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow: var(--neu-shadow-lg);
    background: var(--bg-color);
    padding: 20px;
}

.profile-img {
    width: 380px;
    height: 450px;
    object-fit: cover;
    border-radius: var(--radius-xl);
    display: block;
    box-shadow: var(--neu-inset-sm);
}

.scroll-indicator {
    position: absolute;
    bottom: var(--spacing-lg);
    left: 50%;
    transform: translateX(-50%);
    animation: bounce 2s infinite;
}

.scroll-indicator a {
    color: var(--text-secondary);
    font-size: var(--font-size-xl);
    text-decoration: none;
    background: var(--bg-color);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--neu-shadow-sm);
    transition: var(--transition);
}

.scroll-indicator a:hover {
    box-shadow: var(--neu-inset-sm);
    transform: translateY(-2px);
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

/* Section Styles */
section {
    padding: var(--spacing-2xl) 0;
}

.section-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    text-align: center;
    margin-bottom: var(--spacing-2xl);
    color: var(--text-primary);
}

/* Neumorphism About Section */
.about {
    background: var(--bg-secondary);
    padding: var(--spacing-2xl) 0;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    align-items: center;
}

.about-text p {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    line-height: 1.7;
}

.about-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.stat {
    text-align: center;
    padding: var(--spacing-md);
    background-color: var(--bg-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow);
}

.stat h3 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.stat p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.about-img-container {
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.about-img-container img {
    width: 100%;
    height: 700px;
    object-fit: cover;
}

/* Skills Section */
.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.skill-category {
    background: var(--bg-color);
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--neu-shadow);
    text-align: center;
    transition: var(--transition);
}

.skill-category:hover {
    transform: translateY(-5px);
    box-shadow: var(--neu-shadow-lg);
}

.skill-category h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    color: var(--primary-color);
}

.skill-items {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    justify-content: center;
}

.skill-item {
    background: var(--bg-color);
    color: var(--text-primary);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    font-weight: 500;
    box-shadow: var(--neu-inset-sm);
    transition: var(--transition);
    cursor: pointer;
}

.skill-item:hover {
    box-shadow: var(--neu-shadow-sm);
    transform: translateY(-2px);
    color: var(--primary-color);
}

.skill-item:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* Neumorphism Projects Section */
.projects {
    background: var(--bg-color);
    padding: var(--spacing-2xl) 0;
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
}

.project-card {
    background: var(--bg-color);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--neu-shadow);
    transition: var(--transition);
}

.project-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--neu-shadow-lg);
}

.project-image {
    position: relative;
    overflow: hidden;
    height: 200px;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(224, 229, 236, 0.95);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    opacity: 0;
    transition: var(--transition);
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background: var(--bg-color);
    color: var(--primary-color);
    border-radius: var(--radius-lg);
    text-decoration: none;
    transition: var(--transition);
    box-shadow: var(--neu-shadow-sm);
}

.project-link:hover {
    box-shadow: var(--neu-inset-sm);
    transform: translateY(-2px);
}

.project-link:active {
    box-shadow: var(--neu-inset);
    transform: translateY(0);
}

.project-content {
    padding: var(--spacing-lg);
}

.project-content h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.project-content p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
}

.project-tech {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
}

.project-tech span {
    background-color: var(--bg-secondary);
    color: var(--text-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

/* Contact Section */
.contact {
    padding: var(--spacing-xl) 0;
    background: #e0e5ec;
    min-height: 80vh;
    display: flex;
    align-items: center;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    align-items: center;
}

.contact-info h3 {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.contact-info p {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    line-height: 1.7;
}

.contact-details {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.contact-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    color: var(--text-secondary);
}

.contact-item i {
    width: 20px;
    color: var(--primary-color);
}

/* Neumorphism Contact Form */
.contact-form {
    background: #e0e5ec;
    padding: 40px;
    border-radius: 20px;
    box-shadow: 10px 10px 30px #c2c8d0, -10px -10px 30px #ffffff;
    max-width: 400px;
    margin: 0 auto;
}

.form-group {
    margin-bottom: 20px;
    position: relative;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 15px 20px;
    border-radius: 15px;
    border: none;
    background: #e0e5ec;
    box-shadow: inset 4px 4px 8px #c8ccd1, inset -4px -4px 8px #f0f5fa;
    color: var(--text-primary);
    font-family: 'Inter', 'Segoe UI', sans-serif;
    font-size: 14px;
    transition: all 0.3s ease;
    outline: none;
    box-sizing: border-box;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: #8a8a8a;
    font-weight: 400;
}

.form-group input:focus,
.form-group textarea:focus {
    box-shadow: inset 6px 6px 12px #c2c8d0, inset -6px -6px 12px #ffffff;
    background: #dde3ea;
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
    font-family: inherit;
}

/* Neumorphism Submit Button */
.contact-form .btn {
    width: 100%;
    margin-top: 10px;
    padding: 15px 25px;
    background: #e0e5ec;
    border: none;
    border-radius: 15px;
    cursor: pointer;
    font-weight: 600;
    font-size: 14px;
    color: var(--text-primary);
    box-shadow: 8px 8px 16px #c2c8d0, -8px -8px 16px #ffffff;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.contact-form .btn:hover {
    background: #d6dce4;
    box-shadow: 6px 6px 12px #c2c8d0, -6px -6px 12px #ffffff;
    transform: translateY(-2px);
}

.contact-form .btn:active {
    box-shadow: inset 4px 4px 8px #c2c8d0, inset -4px -4px 8px #ffffff;
    transform: translateY(0);
}

.contact-form .btn.loading {
    opacity: 0.8;
    cursor: not-allowed;
    transform: none;
}

.contact-form .btn i {
    font-size: 14px;
}

/* Neumorphism Form Error States */
.form-group input.error,
.form-group textarea.error {
    background: #f0d5d5;
    box-shadow: inset 4px 4px 8px #d4b8b8, inset -4px -4px 8px #ffffff;
}

.field-error {
    color: #dc3545;
    font-size: 12px;
    margin-top: 8px;
    display: block;
    font-weight: 500;
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    max-width: 400px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification-content {
    padding: 16px 20px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.notification-success {
    border-left: 4px solid #28a745;
}

.notification-error {
    border-left: 4px solid #dc3545;
}

.notification-success .fas {
    color: #28a745;
}

.notification-error .fas {
    color: #dc3545;
}

.notification-content span {
    flex: 1;
    font-size: 14px;
    line-height: 1.4;
}

.notification-close {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.notification-close:hover {
    background-color: #f8f9fa;
}

/* Footer */
.footer {
    background-color: var(--bg-secondary);
    padding: var(--spacing-lg) 0;
    border-top: 1px solid var(--border-color);
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-content p {
    color: var(--text-secondary);
}

.footer-social {
    display: flex;
    gap: var(--spacing-md);
}

.footer-social a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: var(--radius-lg);
    background: var(--bg-color);
    box-shadow: var(--neu-shadow-sm);
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
}

.footer-social a:hover {
   box-shadow: var(--neu-inset-sm);
    color: var(--primary-color);
    transform: translateY(-2px);
}
.footer-social a:active {
    box-shadow: var(--neu-inset);
    transform: translateY(0);
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Success/Error Messages */
.message {
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-md);
    font-weight: 500;
}

.message.success {
    background-color: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.message.error {
    background-color: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

[data-theme="dark"] .message.success {
    background-color: #14532d;
    color: #bbf7d0;
    border-color: #166534;
}

[data-theme="dark"] .message.error {
    background-color: #7f1d1d;
    color: #fecaca;
    border-color: #dc2626;
}
