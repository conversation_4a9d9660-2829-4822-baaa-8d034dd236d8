<<<<<<< Updated upstream
# Personal portfolio
=======
# Personal Portfolio Website

A modern, responsive personal portfolio website built with HTML5, CSS3, and JavaScript. Features a clean, minimal design with smooth animations and professional presentation.

## 🌟 Features

- **Responsive Design**: Mobile-first approach, works on all devices
- **Dark/Light Theme**: Toggle between themes with smooth transitions
- **Smooth Animations**: AOS (Animate On Scroll) library integration
- **Interactive Elements**: Hover effects, smooth scrolling, typing effects
- **Contact Form**: Functional contact form with validation
- **SEO Optimized**: Meta tags, semantic HTML, and accessibility features
- **Performance Optimized**: Compressed assets, efficient code structure
- **Modern Technologies**: CSS Grid, Flexbox, CSS Variables

## 🚀 Technologies Used

- **HTML5**: Semantic markup and accessibility
- **CSS3**: Modern styling with Grid, Flexbox, and Variables
- **JavaScript (ES6+)**: Interactive functionality and animations
- **Font Awesome**: Icon library
- **Google Fonts**: Typography (Inter font family)
- **AOS Library**: Animate On Scroll effects

## 🎨 Sections

1. **Hero Section**: Introduction with profile image and call-to-action
2. **About Section**: Personal information and statistics
3. **Skills Section**: Technical skills organized by category
4. **Projects Section**: Featured projects with descriptions and links
5. **Contact Section**: Contact form and information
6. **Footer**: Social links and copyright

## 🛠️ Setup Instructions
1. **Clone or Download**: Get the project files
2. **Replace Content**: Update the sample data with your information
3. **Add Images**: Replace placeholder images with your photos
4. **Update Links**: Change social media and project links
5. **Customize Colors**: Modify CSS variables for your brand colors
6. **Deploy**: Upload to your hosting provider

## 📝 Customization Guide

### Personal Information
Edit the following in `index.html`:
- Name and title in hero section
- About section content
- Skills and technologies
- Project information
- Contact details
 

### Images
Replace these files in `assets/images/`:
- `profile.jpg` (300x300px recommended)
- `about.jpg` (400x400px recommended)
- `projects/project1.jpg` (600x400px recommended)
- `projects/project2.jpg` (600x400px recommended)
- `projects/project3.jpg` (600x400px recommended)

### Contact Form
The contact form includes client-side validation. To make it functional:
1. Replace the simulation in `js/main.js` with actual form handling
2. Consider using services like Formspree, Netlify Forms, or EmailJS
3. Or implement server-side handling with your preferred backend

## 🌐 Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers (iOS Safari, Chrome Mobile)

## 📱 Responsive Breakpoints

- **Mobile**: 320px - 480px
- **Tablet**: 481px - 768px
- **Desktop**: 769px - 1024px
- **Large Desktop**: 1025px+

## ⚡ Performance Features

- Optimized images and assets
- Efficient CSS with minimal redundancy
- Debounced scroll events
- Lazy loading considerations
- Minimal external dependencies

## 🎯 SEO Features

- Semantic HTML structure
- Meta tags for social sharing
- Descriptive alt text for images
- Proper heading hierarchy
- Fast loading times

## 🔧 Development


## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

Feel free to fork this project and customize it for your own use. If you make improvements, consider sharing them back with the community.

## 📞 Support

If you need help customizing this portfolio, feel free to reach out or create an issue in the repository.

---

**Made with ❤️ for developers who want a professional online presence**
>>>>>>> Stashed changes
My First Project with github
